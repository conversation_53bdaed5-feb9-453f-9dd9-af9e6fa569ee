export type Priority = 'High' | 'Medium' | 'Low';

export type Status = 'In progress' | 'Need to start' | 'Complete' | 'Blocked';

export interface SpreadsheetRow {
  id: number;
  jobRequest: string;
  submitted: string;
  status: Status;
  submitter: string;
  url: string;
  priority: Priority;
  dueDate: string;
  estValue: string;
}

export interface TabItem {
  id: string;
  label: string;
  isActive: boolean;
}

export interface ToolbarButton {
  id: string;
  label: string;
  icon?: string;
  onClick: () => void;
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
}
