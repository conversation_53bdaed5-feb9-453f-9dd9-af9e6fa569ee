'use client';

import { TabItem } from '@/types/spreadsheet';
import { Plus } from 'lucide-react';

interface BottomTabNavigationProps {
  tabs: TabItem[];
  onTabChange: (tabId: string) => void;
}

export default function BottomTabNavigation({ tabs, onTabChange }: BottomTabNavigationProps) {
  const handleAddTab = () => {
    console.log('Add new tab clicked');
  };

  return (
    <div className="bg-white border-t border-gray-200">
      <div className="px-4">
        <nav className="flex items-center space-x-6" aria-label="Bottom tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`
                py-3 px-1 border-b-2 font-medium text-sm transition-colors
                ${tab.isActive
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
              aria-current={tab.isActive ? 'page' : undefined}
            >
              {tab.label}
            </button>
          ))}
          
          {/* Add new tab button */}
          <button
            onClick={handleAddTab}
            className="py-3 px-2 text-gray-400 hover:text-gray-600 transition-colors"
            title="Add new tab"
          >
            <Plus className="w-4 h-4" />
          </button>
        </nav>
      </div>
    </div>
  );
}
