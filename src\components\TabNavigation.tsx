'use client';

import { TabItem } from '@/types/spreadsheet';

interface TabNavigationProps {
  tabs: TabItem[];
  onTabChange: (tabId: string) => void;
}

export default function TabNavigation({ tabs, onTabChange }: TabNavigationProps) {
  return (
    <div className="bg-white">
      <div className="px-4">
        <nav className="flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm transition-colors
                ${tab.isActive
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
              aria-current={tab.isActive ? 'page' : undefined}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
}
