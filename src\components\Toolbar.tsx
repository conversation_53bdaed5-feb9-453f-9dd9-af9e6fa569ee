'use client';

import { 
  EyeOff, 
  ArrowUpDown, 
  Filter, 
  Grid3X3, 
  Download, 
  Upload, 
  Share, 
  Plus,
  ChevronDown 
} from 'lucide-react';

interface ToolbarButtonProps {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  hasDropdown?: boolean;
  variant?: 'default' | 'primary';
}

function ToolbarButton({ icon, label, onClick, hasDropdown, variant = 'default' }: ToolbarButtonProps) {
  const baseClasses = "flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-colors";
  const variantClasses = variant === 'primary' 
    ? "bg-green-600 text-white hover:bg-green-700" 
    : "text-gray-700 hover:text-gray-900 hover:bg-gray-100 border border-gray-300";

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses}`}
    >
      {icon}
      <span>{label}</span>
      {hasDropdown && <ChevronDown className="w-4 h-4" />}
    </button>
  );
}

export default function Toolbar() {
  const handleHideFields = () => {
    console.log('Hide fields clicked');
  };

  const handleSort = () => {
    console.log('Sort clicked');
  };

  const handleFilter = () => {
    console.log('Filter clicked');
  };

  const handleCellView = () => {
    console.log('Cell view clicked');
  };

  const handleImport = () => {
    console.log('Import clicked');
  };

  const handleExport = () => {
    console.log('Export clicked');
  };

  const handleShare = () => {
    console.log('Share clicked');
  };

  const handleNewAction = () => {
    console.log('New Action clicked');
  };

  return (
    <div className="bg-white px-4 py-3 border-b border-gray-200">
      <div className="flex items-center justify-between">
        {/* Left side - Main toolbar buttons */}
        <div className="flex items-center space-x-2">
          <ToolbarButton
            icon={<EyeOff className="w-4 h-4" />}
            label="Hide fields"
            onClick={handleHideFields}
            hasDropdown
          />
          <ToolbarButton
            icon={<ArrowUpDown className="w-4 h-4" />}
            label="Sort"
            onClick={handleSort}
            hasDropdown
          />
          <ToolbarButton
            icon={<Filter className="w-4 h-4" />}
            label="Filter"
            onClick={handleFilter}
          />
          <ToolbarButton
            icon={<Grid3X3 className="w-4 h-4" />}
            label="Cell view"
            onClick={handleCellView}
            hasDropdown
          />
        </div>

        {/* Right side - Action buttons */}
        <div className="flex items-center space-x-2">
          <ToolbarButton
            icon={<Download className="w-4 h-4" />}
            label="Import"
            onClick={handleImport}
          />
          <ToolbarButton
            icon={<Upload className="w-4 h-4" />}
            label="Export"
            onClick={handleExport}
          />
          <ToolbarButton
            icon={<Share className="w-4 h-4" />}
            label="Share"
            onClick={handleShare}
          />
          <ToolbarButton
            icon={<Plus className="w-4 h-4" />}
            label="New Action"
            onClick={handleNewAction}
            variant="primary"
          />
        </div>
      </div>
    </div>
  );
}
