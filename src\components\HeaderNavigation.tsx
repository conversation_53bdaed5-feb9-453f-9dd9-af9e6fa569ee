'use client';

import { <PERSON>, Bell, ChevronRight } from 'lucide-react';
import { BreadcrumbItem } from '@/types/spreadsheet';

const breadcrumbs: BreadcrumbItem[] = [
  { label: 'Workspace' },
  { label: 'Folder 2' },
  { label: 'Spreadsheet 3' }
];

export default function HeaderNavigation() {
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Search submitted');
  };

  const handleNotificationClick = () => {
    console.log('Notifications clicked');
  };

  const handleProfileClick = () => {
    console.log('Profile clicked');
  };

  return (
    <header className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Left side - Breadcrumbs */}
        <nav className="flex items-center space-x-1 text-sm text-gray-600">
          {breadcrumbs.map((item, index) => (
            <div key={index} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="w-4 h-4 mx-1 text-gray-400" />
              )}
              <span 
                className={`${
                  index === breadcrumbs.length - 1 
                    ? 'text-gray-900 font-medium' 
                    : 'text-gray-600 hover:text-gray-900 cursor-pointer'
                }`}
                onClick={() => item.href && console.log('Navigate to:', item.label)}
              >
                {item.label}
              </span>
            </div>
          ))}
        </nav>

        {/* Right side - Search, Notifications, Profile */}
        <div className="flex items-center space-x-4">
          {/* Search */}
          <form onSubmit={handleSearch} className="relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search within sheet"
                className="pl-10 pr-4 py-2 w-64 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </form>

          {/* Notifications */}
          <button
            onClick={handleNotificationClick}
            className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
          >
            <Bell className="w-5 h-5" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>

          {/* User Profile */}
          <button
            onClick={handleProfileClick}
            className="flex items-center space-x-2 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
          >
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
              JD
            </div>
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900">John Doe</div>
              <div className="text-xs text-gray-500"><EMAIL></div>
            </div>
          </button>
        </div>
      </div>
    </header>
  );
}
