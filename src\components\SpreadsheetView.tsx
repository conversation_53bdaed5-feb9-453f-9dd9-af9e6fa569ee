'use client';

import { useState } from 'react';
import Header<PERSON>avi<PERSON> from './HeaderNavigation';
import Tool<PERSON> from './Toolbar';
import TabNavigation from './TabNavigation';
import SpreadsheetGrid from './SpreadsheetGrid';
import BottomTabNavigation from './BottomTabNavigation';
import { SpreadsheetRow, TabItem } from '@/types/spreadsheet';

// Mock data based on the screenshot
const mockData: SpreadsheetRow[] = [
  {
    id: 1,
    jobRequest: 'Launch social media campaign for pro...',
    submitted: '15-11-2024',
    status: 'In progress',
    submitter: '<PERSON>',
    url: 'www.alphapatel...',
    priority: 'Medium',
    dueDate: '20-11-2024',
    estValue: '6,200,000'
  },
  {
    id: 2,
    jobRequest: 'Update press kit for company redesign',
    submitted: '28-10-2024',
    status: 'Need to start',
    submitter: '<PERSON><PERSON><PERSON>',
    url: 'www.irfankhan...',
    priority: 'High',
    dueDate: '30-10-2024',
    estValue: '3,500,000'
  },
  {
    id: 3,
    jobRequest: 'Finalize user testing feedback for app...',
    submitted: '05-12-2024',
    status: 'In progress',
    submitter: '<PERSON>',
    url: 'www.markjohns...',
    priority: 'Medium',
    dueDate: '10-12-2024',
    estValue: '4,750,000'
  },
  {
    id: 4,
    jobRequest: 'Design new features for the website',
    submitted: '10-01-2025',
    status: 'Complete',
    submitter: 'Emily Green',
    url: 'www.emilygreen...',
    priority: 'Low',
    dueDate: '15-01-2025',
    estValue: '5,900,000'
  },
  {
    id: 5,
    jobRequest: 'Prepare financial report for Q1',
    submitted: '25-01-2025',
    status: 'Blocked',
    submitter: 'Jessica Brown',
    url: 'www.jessicabro...',
    priority: 'Low',
    dueDate: '30-01-2025',
    estValue: '2,800,000'
  }
];

const topTabs: TabItem[] = [
  { id: 'abc', label: 'ABC', isActive: true },
  { id: 'question', label: 'Answer a question', isActive: false },
  { id: 'extract', label: 'Extract', isActive: false }
];

const bottomTabs: TabItem[] = [
  { id: 'all', label: 'All Orders', isActive: true },
  { id: 'pending', label: 'Pending', isActive: false },
  { id: 'reviewed', label: 'Reviewed', isActive: false },
  { id: 'arrived', label: 'Arrived', isActive: false }
];

export default function SpreadsheetView() {
  const [data] = useState<SpreadsheetRow[]>(mockData);
  const [activeTopTab, setActiveTopTab] = useState('abc');
  const [activeBottomTab, setActiveBottomTab] = useState('all');

  const handleTopTabChange = (tabId: string) => {
    setActiveTopTab(tabId);
    console.log('Top tab changed to:', tabId);
  };

  const handleBottomTabChange = (tabId: string) => {
    setActiveBottomTab(tabId);
    console.log('Bottom tab changed to:', tabId);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <HeaderNavigation />
      <div className="border-b border-gray-200">
        <Toolbar />
        <TabNavigation
          tabs={topTabs.map(tab => ({ ...tab, isActive: tab.id === activeTopTab }))}
          onTabChange={handleTopTabChange}
        />
      </div>
      <div className="flex-1 overflow-hidden">
        <SpreadsheetGrid data={data} />
      </div>
      <BottomTabNavigation
        tabs={bottomTabs.map(tab => ({ ...tab, isActive: tab.id === activeBottomTab }))}
        onTabChange={handleBottomTabChange}
      />
    </div>
  );
}
