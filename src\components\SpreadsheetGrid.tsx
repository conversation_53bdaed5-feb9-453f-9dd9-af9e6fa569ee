'use client';

import { useMemo, useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
} from '@tanstack/react-table';
import { SpreadsheetRow } from '@/types/spreadsheet';
import StatusBadge from './StatusBadge';
import PriorityIndicator from './PriorityIndicator';

interface SpreadsheetGridProps {
  data: SpreadsheetRow[];
}

export default function SpreadsheetGrid({ data }: SpreadsheetGridProps) {
  const [selectedCell, setSelectedCell] = useState<{ row: number; col: number } | null>(null);

  const columns = useMemo<ColumnDef<SpreadsheetRow>[]>(
    () => [
      {
        id: 'rowNumber',
        header: '',
        cell: ({ row }) => (
          <div className="w-8 text-center text-gray-500 text-sm">
            {row.index + 1}
          </div>
        ),
        size: 40,
      },
      {
        accessorKey: 'jobRequest',
        header: 'Job Request',
        cell: ({ getValue }) => (
          <div className="text-sm text-gray-900 truncate max-w-xs">
            {getValue() as string}
          </div>
        ),
        size: 250,
      },
      {
        accessorKey: 'submitted',
        header: 'Submitted',
        cell: ({ getValue }) => (
          <div className="text-sm text-gray-900">
            {getValue() as string}
          </div>
        ),
        size: 100,
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ getValue }) => (
          <StatusBadge status={getValue() as SpreadsheetRow['status']} />
        ),
        size: 120,
      },
      {
        accessorKey: 'submitter',
        header: 'Submitter',
        cell: ({ getValue }) => (
          <div className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">
            {getValue() as string}
          </div>
        ),
        size: 120,
      },
      {
        accessorKey: 'url',
        header: 'URL',
        cell: ({ getValue }) => (
          <div className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer truncate max-w-xs">
            {getValue() as string}
          </div>
        ),
        size: 150,
      },
      {
        accessorKey: 'priority',
        header: 'Priority',
        cell: ({ getValue }) => (
          <PriorityIndicator priority={getValue() as SpreadsheetRow['priority']} />
        ),
        size: 100,
      },
      {
        accessorKey: 'dueDate',
        header: 'Due Date',
        cell: ({ getValue }) => (
          <div className="text-sm text-gray-900">
            {getValue() as string}
          </div>
        ),
        size: 100,
      },
      {
        accessorKey: 'estValue',
        header: 'Est. Value',
        cell: ({ getValue }) => (
          <div className="text-sm text-gray-900 font-medium">
            {getValue() as string}
          </div>
        ),
        size: 120,
      },
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleCellClick = (rowIndex: number, colIndex: number) => {
    setSelectedCell({ row: rowIndex, col: colIndex });
    console.log('Cell clicked:', { row: rowIndex, col: colIndex });
  };

  return (
    <div className="bg-white flex-1 overflow-hidden">
      <div className="h-full overflow-auto">
        <table className="w-full border-collapse table-fixed">
          <thead className="sticky top-0 z-10">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id} className="border-b border-gray-200">
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-r border-gray-200 last:border-r-0 resize-x"
                    style={{ width: header.getSize() }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="bg-white">
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id} className="border-b border-gray-100 hover:bg-gray-50">
                {row.getVisibleCells().map((cell, cellIndex) => (
                  <td
                    key={cell.id}
                    className={`px-3 py-2 border-r border-gray-100 last:border-r-0 cursor-pointer transition-colors ${
                      selectedCell?.row === row.index && selectedCell?.col === cellIndex
                        ? 'bg-blue-50 ring-1 ring-blue-400'
                        : ''
                    }`}
                    onClick={() => handleCellClick(row.index, cellIndex)}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Add empty rows to fill the view */}
      <div>
        {Array.from({ length: 15 }, (_, index) => (
          <div key={index} className="flex border-b border-gray-100 hover:bg-gray-50">
            <div className="w-10 px-3 py-2 text-center text-gray-400 text-sm border-r border-gray-100 bg-gray-50">
              {data.length + index + 1}
            </div>
            {columns.slice(1).map((_, colIndex) => (
              <div
                key={colIndex}
                className="px-3 py-2 border-r border-gray-100 last:border-r-0 cursor-pointer min-h-[40px] flex items-center"
                style={{ width: columns[colIndex + 1]?.size || 120 }}
                onClick={() => handleCellClick(data.length + index, colIndex + 1)}
              >
                {colIndex === 0 && index === 0 && (
                  <div className="w-20 h-6 border border-gray-300 bg-white rounded"></div>
                )}
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}
