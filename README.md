# Spreadsheet Prototype

A pixel-perfect React spreadsheet prototype built with Next.js, TypeScript, and Tailwind CSS that replicates a Google Sheets/Excel-like interface.

## 🚀 Live Demo

The application is running at: [http://localhost:3001](http://localhost:3001)

## 🛠️ Tech Stack

- **React 18** - Modern React with hooks
- **Next.js 15** - Full-stack React framework with Turbopack
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **@tanstack/react-table** - Powerful table component
- **Lucide React** - Beautiful icons

## ✨ Features

### Core Functionality
- ✅ Pixel-perfect layout matching the Figma design
- ✅ Google Sheets/Excel-like spreadsheet experience
- ✅ Interactive cell selection and navigation
- ✅ Responsive design that works on all screen sizes

### UI Components
- ✅ **Header Navigation** - Breadcrumbs, search, notifications, user profile
- ✅ **Toolbar** - Hide fields, Sort, Filter, Cell view, Import, Export, Share, New Action
- ✅ **Tab Navigation** - ABC, Answer a question, Extract tabs
- ✅ **Data Grid** - Sortable columns with proper data types
- ✅ **Status Badges** - Color-coded status indicators (In progress, Need to start, Complete, Blocked)
- ✅ **Priority Indicators** - High, Medium, Low priority levels
- ✅ **Bottom Tabs** - All Orders, Pending, Reviewed, Arrived

### Interactive Features
- ✅ All buttons and tabs are functional with console logging
- ✅ Hover states and transitions
- ✅ Cell selection with visual feedback
- ✅ Tab switching functionality
- ✅ No dead UI elements

## 🏗️ Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and Tailwind imports
│   ├── layout.tsx           # Root layout component
│   └── page.tsx             # Main page component
├── components/
│   ├── SpreadsheetView.tsx  # Main container component
│   ├── HeaderNavigation.tsx # Top navigation bar
│   ├── Toolbar.tsx          # Action toolbar
│   ├── TabNavigation.tsx    # Top tab navigation
│   ├── SpreadsheetGrid.tsx  # Main data grid
│   ├── StatusBadge.tsx      # Status indicator component
│   ├── PriorityIndicator.tsx# Priority indicator component
│   └── BottomTabNavigation.tsx # Bottom tab navigation
└── types/
    └── spreadsheet.ts       # TypeScript type definitions
```

## 🚦 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd spreadsheet-prototype
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🎯 Design Implementation

The prototype closely follows the provided Figma design with:

- **Exact color scheme** - Matching status colors, priority indicators, and UI elements
- **Proper spacing** - Consistent padding, margins, and layout
- **Typography** - System fonts with proper weights and sizes
- **Interactive states** - Hover effects, active states, and transitions
- **Grid layout** - Proper column sizing and row alignment

## 🔧 Technical Decisions & Trade-offs

### Component Architecture
- **Modular design** - Each UI section is a separate component for maintainability
- **TypeScript interfaces** - Strong typing for data structures and props
- **React hooks** - Modern state management with useState

### Styling Approach
- **Tailwind CSS** - Utility-first approach for rapid development
- **Responsive design** - Mobile-first approach with proper breakpoints
- **Custom scrollbars** - Enhanced UX with styled scrollbars

### Data Management
- **Mock data** - Static data matching the design for prototype purposes
- **Local state** - Simple useState for tab management and cell selection
- **Console logging** - All interactions logged for demonstration

### Performance Considerations
- **@tanstack/react-table** - Efficient table rendering and column management
- **Sticky headers** - Better UX for large datasets
- **Optimized builds** - Next.js optimization for production

## 🧪 Quality Assurance

- ✅ **ESLint** - No linting errors
- ✅ **TypeScript** - Strict mode with no type errors
- ✅ **Build process** - Successful production build
- ✅ **Manual testing** - All interactive elements tested

## 🚀 Deployment

The project is ready for deployment on platforms like:
- Vercel (recommended for Next.js)
- Netlify
- AWS Amplify
- Any static hosting service

## 📝 Future Enhancements

If time permits, these stretch features could be added:
- Keyboard navigation (arrow keys)
- Column resize functionality
- Column hide/show toggles
- Data persistence
- Real-time collaboration
- Advanced filtering and sorting

## 🤝 Contributing

This is a prototype project. For production use, consider:
- Adding proper error handling
- Implementing data persistence
- Adding comprehensive tests
- Optimizing for larger datasets
- Adding accessibility features
